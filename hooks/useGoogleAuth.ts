import { useState } from 'react';
import Constants from 'expo-constants';
import { supabase } from '@/lib/supabase';
import { useAuthUser } from '@/hooks/useAuthUser';
import { useSplash } from '@/context/SplashContext';
import {
  handleAuthError,
  getAuthErrorMessage,
} from '@/utils/auth-error-handler';

// Conditional import for Google Sign-In (only works with development builds, not Expo Go)
let GoogleSignin: any = null;
let statusCodes: any = null;

// Check if we're in Expo Go (where Google Sign-In won't work)
const isExpoGo = Constants.executionEnvironment === 'storeClient';

// Only import Google Sign-In in development builds
if (!isExpoGo) {
  try {
    const googleSigninModule = require('@react-native-google-signin/google-signin');
    GoogleSignin = googleSigninModule.GoogleSignin;
    statusCodes = googleSigninModule.statusCodes;
  } catch (error) {
    console.warn(
      'Google Sign-In module not available. Make sure you have run "expo prebuild" and "expo run:android/ios":',
      error
    );
  }
}

export function useGoogleAuth() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setUser } = useAuthUser();
  const { resetSplash } = useSplash();

  // Configure Google Sign-In
  const configureGoogleSignIn = () => {
    if (!GoogleSignin) {
      throw new Error('Google Sign-In not available');
    }

    const webClientId = process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID;

    if (!webClientId) {
      throw new Error('Google Web Client ID not configured');
    }

    GoogleSignin.configure({
      webClientId,
      offlineAccess: false,
    });
  };

  const signInWithGoogle = async (): Promise<boolean> => {
    setLoading(true);
    setError(null);

    try {
      // Check if Google Sign-In is available (not in Expo Go)
      if (isExpoGo || !GoogleSignin) {
        setError(
          'Google Sign-In requires a development build. Please run "expo prebuild" then "expo run:android" or "expo run:ios"'
        );
        return false;
      }

      // Configure Google Sign-In if not already configured
      configureGoogleSignIn();

      // Check if Google Play Services are available
      await GoogleSignin.hasPlayServices();

      // Sign in with Google
      const userInfo = await GoogleSignin.signIn();

      if (!userInfo.data?.idToken) {
        throw new Error('No ID token received from Google');
      }

      // Sign in with Supabase using the Google ID token
      const { data, error: signInError } =
        await supabase.auth.signInWithIdToken({
          provider: 'google',
          token: userInfo.data.idToken,
        });

      if (signInError) {
        await handleAuthError(signInError, false);
        const errorMessage = signInError.code
          ? getAuthErrorMessage(signInError.code)
          : signInError.message || 'Something went wrong';
        setError(errorMessage);
        return false;
      }

      if (data.user) {
        setUser(data.user);
        resetSplash();
      }

      return true;
    } catch (error: any) {
      await handleAuthError(error, false);

      // Handle specific Google Sign-In errors
      if (statusCodes && error.code === statusCodes.SIGN_IN_CANCELLED) {
        setError('Sign in was cancelled');
      } else if (statusCodes && error.code === statusCodes.IN_PROGRESS) {
        setError('Sign in is already in progress');
      } else if (
        statusCodes &&
        error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE
      ) {
        setError('Google Play Services not available');
      } else {
        setError('Something went wrong');
      }
      return false;
    } finally {
      setLoading(false);
    }
  };

  return { signInWithGoogle, loading, error, setError };
}
