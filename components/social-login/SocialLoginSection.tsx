import React from 'react';
import { View } from 'react-native';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Text } from '@/components/ui/text';
import { Divider } from '@/components/ui/divider';
import { GoogleSignInButton } from './GoogleSignInButton';
import { toast } from '@/toast/toast';
import { useRouter } from 'expo-router';

interface SocialLoginSectionProps {
  title?: string;
}

export function SocialLoginSection({ title = "Or continue with" }: SocialLoginSectionProps) {
  const router = useRouter();

  const handleSocialLoginSuccess = () => {
    // Navigate back or to main app after successful login
    router.back();
  };

  const handleSocialLoginError = (error: string) => {
    toast.error(error);
  };

  return (
    <VStack space="md" className="w-full">
      {/* Divider with text */}
      <HStack className="items-center w-full" space="md">
        <Divider className="flex-1" />
        <Text className="text-typography-500 font-urbanist text-sm px-2">
          {title}
        </Text>
        <Divider className="flex-1" />
      </HStack>

      {/* Social Login Buttons */}
      <VStack space="sm" className="w-full">
        <GoogleSignInButton
          onSuccess={handleSocialLoginSuccess}
          onError={handleSocialLoginError}
        />
      </VStack>
    </VStack>
  );
}
