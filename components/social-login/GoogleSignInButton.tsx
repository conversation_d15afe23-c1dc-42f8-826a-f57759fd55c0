import React from 'react';
import { GoogleSigninButton } from '@react-native-google-signin/google-signin';
import { useGoogleAuth } from '@/hooks/useGoogleAuth';

interface GoogleSignInButtonProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export function GoogleSignInButton({ onSuccess, onError }: GoogleSignInButtonProps) {
  const { signInWithGoogle, loading } = useGoogleAuth();

  const handleGoogleSignIn = async () => {
    try {
      const success = await signInWithGoogle();
      if (success) {
        onSuccess?.();
      } else {
        onError?.('Something went wrong');
      }
    } catch (error: any) {
      onError?.(error.message || 'Something went wrong');
    }
  };

  return (
    <GoogleSigninButton
      size={GoogleSigninButton.Size.Wide}
      color={GoogleSigninButton.Color.Dark}
      onPress={handleGoogleSignIn}
      disabled={loading}
    />
  );
}
