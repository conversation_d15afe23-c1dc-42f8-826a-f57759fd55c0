# Google Sign-In Setup for React Native

Google Sign-In implementation following Supabase documentation exactly for React Native native flow.

## Setup Steps

1. **Configure Google Cloud Console:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Google+ API
   - Create OAuth 2.0 credentials:
     - For Android: Use your app's package name: `in.kali` (from app.json)
     - For iOS: Use your app's Bundle ID
     - For Web: Create a Web client ID (this is what you'll use in your app)
   - Add your SHA-1 certificate fingerprint (for development and production)

2. **Configure Supabase:**
   - Go to your Supabase Dashboard
   - Navigate to Authentication > Providers
   - Enable Google provider
   - Add your **web client ID** (not the Android/iOS client ID)

3. **Update your app:**
   ```bash
   # Copy .env.example to .env
   cp .env.example .env

   # Edit .env and add your web client ID
   EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_web_client_id.apps.googleusercontent.com
   ```

4. **Get SHA-1 Certificate Fingerprint:**
   ```bash
   # For development (debug keystore)
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android

   # For production, use your release keystore
   keytool -list -v -keystore your-release-key.keystore -alias your-key-alias
   ```

## Implementation Details

The implementation follows the exact pattern from Supabase documentation:

- Uses `@react-native-google-signin/google-signin` package
- Configures with web client ID
- Uses native sign-in flow (not web OAuth)
- Integrates with Supabase Auth using `signInWithIdToken`

## Usage

The Google Sign-In button is automatically included in login and signup screens via the `SocialLoginSection` component.

## Adding Other Social Logins (e.g., Apple)

The architecture is designed to be easily extensible:

1. Create a new button component (e.g., `AppleSignInButton.tsx`)
2. Create a corresponding hook (e.g., `useAppleAuth.ts`)
3. Add the button to `SocialLoginSection.tsx`

Example structure for Apple Sign-In:
```typescript
// hooks/useAppleAuth.ts
export function useAppleAuth() {
  // Apple authentication logic
}

// components/social-login/AppleSignInButton.tsx
export function AppleSignInButton() {
  // Apple sign-in button component
}
```

## Important Notes

- This implementation uses the native flow (not web OAuth)
- You need both platform-specific OAuth credentials AND web client ID
- The web client ID is used for token verification with Supabase
- Make sure to add both development and production SHA-1 fingerprints

## Testing

1. Set up your environment variables
2. Build and run on device/emulator
3. Test the Google Sign-In flow
4. Verify user creation in Supabase dashboard
