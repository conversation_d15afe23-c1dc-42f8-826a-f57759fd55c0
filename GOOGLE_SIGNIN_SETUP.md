# Google Sign-In Integration

This project now includes Google Sign-In integration following the official Supabase documentation for React Native.

## Quick Setup

1. **Configure Google Cloud Console:**
   - Create OAuth 2.0 credentials (Web client ID)
   - Add your Android/iOS app credentials
   - Get SHA-1 fingerprints for Android

2. **Configure Supabase:**
   - Enable Google provider in Authentication settings
   - Add your **web client ID** to Supabase

3. **Set Environment Variable:**
   ```bash
   # Copy .env.example to .env
   cp .env.example .env
   
   # Add your web client ID
   EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_web_client_id.apps.googleusercontent.com
   ```

## Implementation Details

### Files Created/Modified:
- `hooks/useGoogleAuth.ts` - Google authentication logic
- `components/social-login/GoogleSignInButton.tsx` - Google sign-in button
- `components/social-login/SocialLoginSection.tsx` - Social login section
- `pages/LoginComponent.tsx` - Added social login to login screen
- `app/account/sign-up.tsx` - Added social login to signup screen

### Architecture Benefits:
- ✅ Follows official Supabase documentation exactly
- ✅ Uses native Google Sign-In flow (not web OAuth)
- ✅ Easily extensible for other social logins (Apple, Facebook, etc.)
- ✅ Consistent error handling and loading states
- ✅ Integrates with existing authentication flow

## Adding Other Social Logins

To add Apple Sign-In or other providers:

1. Create a new hook: `hooks/useAppleAuth.ts`
2. Create a new button: `components/social-login/AppleSignInButton.tsx`
3. Add to `SocialLoginSection.tsx`

The architecture is designed to make this simple and consistent.

## Testing

1. Set up your Google Cloud Console and Supabase configuration
2. Add your environment variables
3. Build and test on a real device (Google Sign-In requires real device for testing)

For detailed setup instructions, see `docs/SOCIAL_LOGIN_SETUP.md`.
